extends Node

enum GameState { HUB, IN_LEVEL, TRANSITIONING }

signal state_changed(new_state: GameState)

var current_state: GameState = GameState.TRANSITIONING

func set_state(new_state: GameState) -> void:
	if current_state == new_state:
		return
	current_state = new_state
	state_changed.emit(new_state)

func get_current_state() -> GameState:
	return current_state

func is_gameplay_active() -> bool:
	return current_state == GameState.IN_LEVEL or current_state == GameState.HUB
