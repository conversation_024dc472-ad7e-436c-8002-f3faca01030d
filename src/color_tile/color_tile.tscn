[gd_scene load_steps=5 format=3 uid="uid://2o2nqedmdng0"]

[ext_resource type="Script" uid="uid://4nvcx314nnga" path="res://src/color_tile/color_tile.gd" id="1_82ejh"]
[ext_resource type="Texture2D" uid="uid://dvgogbmr7vcxt" path="res://assets/tiles_sheet.png" id="1_dy8m4"]

[sub_resource type="AtlasTexture" id="AtlasTexture_82ejh"]
atlas = ExtResource("1_dy8m4")
region = Rect2(0, 0, 8, 8)

[sub_resource type="CircleShape2D" id="CircleShape2D_dy8m4"]
radius = 3.0

[node name="ColorTile" type="Node2D" node_paths=PackedStringArray("color_rect") groups=["color_tiles"]]
script = ExtResource("1_82ejh")
color_rect = NodePath("ColorRect")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture_filter = 1
texture = SubResource("AtlasTexture_82ejh")

[node name="ColorRect" type="ColorRect" parent="."]
visible = false
offset_left = -4.0
offset_top = -4.0
offset_right = 3.0
offset_bottom = 3.0

[node name="Area2D" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="Area2D"]
shape = SubResource("CircleShape2D_dy8m4")
