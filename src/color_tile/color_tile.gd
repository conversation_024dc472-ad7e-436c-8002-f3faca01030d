class_name ColorTile
extends Node2D

signal painted
signal erased

@export var color_rect: ColorRect

func _ready() -> void:
	add_to_group("tiles")

func paint(color: Color) -> void:
	if is_painted():
		return

	color_rect.color = color
	color_rect.show()

	painted.emit()

func is_painted() -> bool:
	return color_rect.visible

func unpaint() -> void:
	if not is_painted():
		return

	color_rect.hide()
	erased.emit()
