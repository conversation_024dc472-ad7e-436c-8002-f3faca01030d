class_name PlayerBaseStatsData
extends Resource

@export_group("Core Stats")
@export var max_health: int = 1
@export var max_paint: int = 10
@export var move_duration: float = 0.15
@export var extra_lives: int = 0
@export var size_modifier: float = 1.0

@export_group("Movement")
@export var tile_size: int = 8

@export_group("Input")
@export var initial_repeat_delay: float = 0.15
@export var repeat_rate: float = 0.1

@export_group("Other Properties")
@export var paint_color: Color = Color.WHITE

func get_stat_value(stat_id: StringName) -> Variant:
	match stat_id:
		"max_health":
			return max_health
		"max_paint":
			return max_paint
		"move_duration":
			return move_duration
		"extra_lives":
			return extra_lives
		"size_modifier":
			return size_modifier
		"tile_size":
			return tile_size
		"initial_repeat_delay":
			return initial_repeat_delay
		"repeat_rate":
			return repeat_rate
		"paint_color":
			return paint_color
		_:
			push_error("Unknown stat_id: " + str(stat_id))
			return null
