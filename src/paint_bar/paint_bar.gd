class_name PaintBar
extends Node2D

@export var player_service: PlayerService
@export var drops_container: HBoxContainer
@export var frame: NinePatchRect
@export var drop_scene: PackedScene

func _ready() -> void:
	player_service.player_initialized.connect(_on_player_initialized)

func _on_player_initialized(_player_node: Node2D) -> void:
	var paint_component: PaintComponent = player_service.get_paint_component()
	paint_component.paint_changed.connect(_redraw)
	_redraw(paint_component.current_paint, paint_component.max_paint)

func _redraw(current: int, max_val: int) -> void:
	for child in drops_container.get_children():
		child.queue_free()

	for i in range(max_val):
		var drop_instance: Node = drop_scene.instantiate()
		drops_container.add_child(drop_instance)
		if drop_instance is CanvasItem:
			(drop_instance as CanvasItem).visible = (i < current)

	_resize_frame(max_val)


func _resize_frame(max_paint: int) -> void:
	if frame == null or max_paint <= 0:
		return

	if drops_container.get_child_count() == 0:
		return

	var drop: Control = drops_container.get_child(0) as Control
	if not is_instance_valid(drop):
		return

	var drop_w: float = drop.custom_minimum_size.x

	var sep: int = drops_container.get_theme_constant("separation")
	var mc: MarginContainer = drops_container.get_parent() as MarginContainer
	var ml: int = mc.get_theme_constant("margin_left")
	var content_w: float = max_paint * drop_w * sep + ml + 0.5

	frame.custom_minimum_size.x = content_w
	frame.size = Vector2(content_w, frame.size.y)

	var viewport: Viewport = get_viewport()
	if viewport == null:
		return

	var viewport_size: Vector2 = viewport.get_visible_rect().size
	frame.position.x = - content_w / 2.0
	global_position.x = viewport_size.x / 2.0
