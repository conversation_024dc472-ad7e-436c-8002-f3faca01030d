[gd_scene load_steps=6 format=3 uid="uid://se1apgxlfmx6"]

[ext_resource type="Texture2D" uid="uid://biagw5gvp674n" path="res://assets/color_drop.png" id="1_abcde"]
[ext_resource type="PackedScene" uid="uid://bqxh8h8r1m5v4" path="res://src/paint_refill/paint_refill_component.tscn" id="2_fghij"]
[ext_resource type="Script" uid="uid://dvf5124sabmon" path="res://src/paint_refill/paint_refill_data.gd" id="3_klmno"]

[sub_resource type="CircleShape2D" id="CircleShape2D_67890"]
radius = 1.4

[sub_resource type="Resource" id="Resource_12345"]
script = ExtResource("3_klmno")
refill_amount = 5

[node name="PaintRefillDrop" type="Node2D" groups=["paint_refills"]]

[node name="Sprite2D" type="Sprite2D" parent="."]
texture_filter = 1
position = Vector2(0, -2.97)
texture = ExtResource("1_abcde")

[node name="InteractionArea" type="Area2D" parent="."]
position = Vector2(0, -3)
collision_layer = 0

[node name="CollisionShape2D" type="CollisionShape2D" parent="InteractionArea"]
position = Vector2(-0.48, 0.6)
shape = SubResource("CircleShape2D_67890")

[node name="PaintRefillComponent" parent="." instance=ExtResource("2_fghij")]
data = SubResource("Resource_12345")
