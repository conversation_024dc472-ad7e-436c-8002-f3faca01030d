class_name MovementComponent
extends Node

signal movement_started(direction: Vector2)
signal movement_completed()
signal movement_blocked(direction: Vector2)

@export var data: MovementData
@export var actor: CharacterBody2D
@export var collision_ray: RayCast2D

var is_moving: bool = false
var start_position: Vector2 = Vector2.ZERO
var target_position: Vector2 = Vector2.ZERO
var progress: float = 0.0

func _ready() -> void:
    add_to_group("movement_components")
