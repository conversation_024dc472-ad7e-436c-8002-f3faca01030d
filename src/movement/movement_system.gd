class_name MovementSystem
extends Node

@export var player_service: PlayerService
@export var tile_query_system: TileQuerySystem

func _ready() -> void:
	player_service.player_initialized.connect(_on_player_initialized)

func _on_player_initialized(_player_node: Node2D) -> void:
	_snap_player_to_grid.call_deferred()

func _process(delta: float) -> void:
	var components: Array[Node] = get_tree().get_nodes_in_group(&"movement_components")
	for node: Node in components:
		var component: MovementComponent = node as MovementComponent
		if component == null or component.data == null or component.actor == null:
			continue

		if not component.is_moving:
			continue

		var move_duration: float = StatService.get_move_duration()
		var increment: float = delta / max(0.001, move_duration)
		component.progress = move_toward(component.progress, 1.0, increment)
		component.actor.position = component.start_position.lerp(component.target_position, component.progress)

		if is_equal_approx(component.progress, 1.0):
			component.actor.position = component.target_position
			component.is_moving = false
			component.progress = 0.0
			component.movement_completed.emit()

func move(component: MovementComponent, direction: Vector2) -> void:
	if component == null or component.data == null or component.actor == null or component.collision_ray == null:
		return

	if component.is_moving:
		return

	var target_pos: Vector2 = direction * StatService.get_tile_size()
	component.collision_ray.target_position = target_pos
	component.collision_ray.force_raycast_update()

	if component.collision_ray.is_colliding():
		component.movement_blocked.emit(direction)
		return

	component.start_position = component.actor.position
	component.target_position = component.actor.position + target_pos
	component.is_moving = true
	component.progress = 0.0
	component.movement_started.emit(direction)

func teleport(component: MovementComponent, global_target_pos: Vector2) -> void:
	if component == null or component.data == null or component.actor == null:
		return

	if component.is_moving:
		return

	if component.collision_ray != null:
		var saved_mask: int = component.collision_ray.collision_mask
		component.collision_ray.collision_mask = 0

		component.movement_completed.connect(
			func() -> void:
				if is_instance_valid(component) and is_instance_valid(component.collision_ray):
					component.collision_ray.collision_mask = saved_mask,
					CONNECT_ONE_SHOT
		)

	var local_target_pos: Vector2 = (component.actor.get_parent() as Node2D).to_local(global_target_pos)

	component.start_position = component.actor.position
	component.target_position = local_target_pos
	component.is_moving = true
	component.progress = 0.0
	component.movement_started.emit(local_target_pos - component.start_position)

func _snap_player_to_grid() -> void:
	var player: Node2D = player_service.get_player_node()
	var nearest_tile: Node2D = tile_query_system.get_nearest_tile(player.global_position)
	if is_instance_valid(nearest_tile):
		player.global_position = nearest_tile.global_position
