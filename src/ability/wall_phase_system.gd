class_name WallPhaseSystem
extends Node

@export var tile_query_system: TileQuerySystem
@export var movement_system: MovementSystem
@export var player_service: PlayerService

var _player_node: Node2D
var _paint_component: PaintComponent
var _ability_component: AbilityComponent
var _movement_component: MovementComponent
var _phase_ability: WallPhaseAbilityData

var _is_phasing: bool = false

func _ready() -> void:
    player_service.player_initialized.connect(_on_player_initialized)

func _on_player_initialized(_player_node_param: Node2D) -> void:
    _player_node = player_service.get_player_node()
    _paint_component = player_service.get_paint_component()
    _ability_component = player_service.get_ability_component()
    _movement_component = player_service.get_movement_component()

    var phase_ability_node: Ability = _ability_component.get_ability(WallPhaseAbilityData)
    if not is_instance_valid(phase_ability_node):
        return
    _phase_ability = phase_ability_node.data as WallPhaseAbilityData

    if is_instance_valid(_movement_component):
        _movement_component.movement_blocked.connect(_on_movement_blocked)


func _on_movement_blocked(direction: Vector2) -> void:
    if _is_phasing:
        return

    if _paint_component.current_paint < _phase_ability.paint_cost:
        return

    var target_tile: Node2D = _find_passable_tile(direction)
    if not is_instance_valid(target_tile):
        return

    _paint_component.current_paint -= _phase_ability.paint_cost

    _is_phasing = true
    movement_system.teleport(_movement_component, target_tile.global_position)
    _movement_component.movement_completed.connect(
        func() -> void:
            _is_phasing = false,
            CONNECT_ONE_SHOT
    )

func _find_passable_tile(direction: Vector2) -> Node2D:
    var step: int = 8
    if is_instance_valid(_movement_component):
        step = _movement_component.data.tile_size


    var pos: Vector2 = _player_node.global_position + direction * step
    var limit: int = 64
    while limit > 0:
        var tile: Node2D = tile_query_system.get_tile_at_global_pos(pos)
        if is_instance_valid(tile):
            return tile
        pos += direction * step
        limit -= 1
    return null
