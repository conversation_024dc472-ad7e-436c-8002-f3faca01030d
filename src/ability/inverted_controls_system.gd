class_name InvertedControlsSystem
extends Node

@export var player_service: PlayerService

var _player_ability_component: AbilityComponent
var _player_movement_component: MovementComponent
var _is_player_moving: bool = false

func _ready() -> void:
	player_service.player_initialized.connect(_on_player_initialized)

func _on_player_initialized(_player_node: Node2D) -> void:
	_player_ability_component = player_service.get_ability_component()
	_player_movement_component = player_service.get_movement_component()

	if is_instance_valid(_player_movement_component):
		_player_movement_component.movement_started.connect(_on_player_movement_started)
		_player_movement_component.movement_completed.connect(_on_player_movement_completed)

func _process(delta: float) -> void:
	if not is_instance_valid(_player_ability_component):
		return

	var inverted_ability: InvertedControlsAbility = _player_ability_component.get_ability(InvertedControlsAbilityData) as InvertedControlsAbility
	if not is_instance_valid(inverted_ability):
		return

	var ability_data: InvertedControlsAbilityData = inverted_ability.data as InvertedControlsAbilityData
	if inverted_ability.remaining_time == 0.0 and ability_data.duration > 0.0:
		inverted_ability.initialize()

	var effective_delta: float = delta
	if _is_player_moving:
		effective_delta *= ability_data.time_scale_on_move

	inverted_ability.remaining_time -= effective_delta

	if inverted_ability.remaining_time <= 0.0:
		_player_ability_component.remove_ability(inverted_ability)

func _on_player_movement_started(_direction: Vector2) -> void:
	_is_player_moving = true

func _on_player_movement_completed() -> void:
	_is_player_moving = false
