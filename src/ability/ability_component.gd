class_name AbilityComponent
extends Node

@export var ability_data_list: Array[AbilityData]

func _ready() -> void:
	_initialize_abilities()

func _initialize_abilities() -> void:
	for ability_data: AbilityData in ability_data_list:
		_add_ability(ability_data)

func _add_ability(ability_data: AbilityData) -> void:
	var ability_node: Ability = null

	if ability_data is InvertedControlsAbilityData:
		ability_node = InvertedControlsAbility.new()
	else:
		ability_node = Ability.new()

	ability_node.data = ability_data
	add_child(ability_node)

func get_ability(ability_type: GDScript) -> Ability:
	for child: Node in get_children():
		if child is Ability:
			var ability: Ability = child as Ability
			if ability.data != null and ability.data.get_script() == ability_type:
				return ability
	return null

func has_ability(ability_type: GDScript) -> bool:
	return get_ability(ability_type) != null

func remove_ability(ability: Ability) -> void:
	if ability != null and ability.get_parent() == self:
		ability.queue_free()
