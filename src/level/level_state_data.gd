class_name LevelStateData
extends Resource

enum State {PLAYING, WON, LOST}

signal state_changed(new_state: State)
signal progress_changed(new_progress: float)
signal event_triggered(event_index: int)

@export var events: Array[LevelEventData]

var _triggered_events: Array[bool]

var total_tiles: int = 0
var state: State = State.PLAYING:
	set(new_state):
		if state == new_state:
			return
		state = new_state
		state_changed.emit(state)

var painted_tiles: int = 0:
	set(new_painted_count):
		if painted_tiles == new_painted_count:
			return
		painted_tiles = new_painted_count
		progress_changed.emit(get_progress())

func get_progress() -> float:
	if total_tiles <= 0:
		return 0.0
	return float(painted_tiles) / float(total_tiles)

func trigger_event(index: int) -> void:
	if index < 0 or index >= _triggered_events.size():
		return
	_triggered_events[index] = true
	event_triggered.emit(index)

func reset() -> void:
	total_tiles = 0
	painted_tiles = 0
	state = State.PLAYING

	if not events.is_empty():
		_triggered_events.resize(events.size())
		_triggered_events.fill(false)
