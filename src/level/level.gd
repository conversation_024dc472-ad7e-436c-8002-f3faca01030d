class_name Level
extends BaseLevel

signal level_completed
signal level_failed

@export var level_id: String = ""
@export var level_state_component: LevelStateComponent
@export var loss_ui: <PERSON>vas<PERSON>ayer

func _ready() -> void:
	super._ready()
	level_state_component.data.state_changed.connect(_on_level_state_changed)

	if loss_ui.has_signal("play_again_pressed"):
		loss_ui.connect("play_again_pressed", _on_level_failed)

func prepare_scene() -> void:
	tile_query_system.build_map()
	_setup_level()

func _setup_level() -> void:
	var tiles: Array[Node] = get_tree().get_nodes_in_group(&"color_tiles")
	level_state_component.data.total_tiles = tiles.size()

	for tile_node: ColorTile in tiles:
		if tile_node is ColorTile:
			tile_node.painted.connect(_on_tile_painted)


func _on_level_state_changed(new_state: LevelStateData.State) -> void:
	if new_state == LevelStateData.State.LOST:
		loss_ui.show()

func _on_tile_painted() -> void:
	level_state_component.data.painted_tiles += 1

func _on_level_completed() -> void:
	level_completed.emit()

func _on_level_failed() -> void:
	level_failed.emit()
