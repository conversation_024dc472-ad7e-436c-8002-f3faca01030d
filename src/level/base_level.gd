class_name BaseLevel
extends Node2D

signal ready_for_spawn

@export var tile_query_system: TileQuerySystem
@export var player_service: PlayerService

func _ready() -> void:
	ready_for_spawn.emit.call_deferred()

func prepare_scene() -> void:
	pass

func setup_and_spawn_player(player_scene: PackedScene) -> void:
	var spawn_tile: Node2D = tile_query_system.get_random_tile()
	if not is_instance_valid(spawn_tile):
		push_error("BaseLevel: No spawnable tiles found.")
		return

	var player_instance: Node2D = player_scene.instantiate() as Node2D
	if not is_instance_valid(player_instance):
		push_error("BaseLevel: Failed to instantiate player scene.")
		return

	add_child(player_instance)
	player_instance.global_position = spawn_tile.global_position

	player_service.initialize(player_instance)
