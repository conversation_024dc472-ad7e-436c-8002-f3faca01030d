extends Node

signal win_condition_met

@export var level_state_component: LevelStateComponent
@export var win_condition_systems: Array[ConditionSystem]
@export var loss_condition_systems: Array[ConditionSystem]

func _process(_delta: float) -> void:
	if not GameStateService.is_gameplay_active():
		return

	var data: LevelStateData = level_state_component.data
	if data.state != LevelStateData.State.PLAYING:
		return

	var all_win_conditions_met: bool = true
	if win_condition_systems.is_empty():
		all_win_conditions_met = false
	else:
		for system: ConditionSystem in win_condition_systems:
			if not system.is_met():
				all_win_conditions_met = false
				break

	if all_win_conditions_met:
		data.state = LevelStateData.State.WON
		win_condition_met.emit()
		return

	for system: ConditionSystem in loss_condition_systems:
		if system.is_met():
			data.state = LevelStateData.State.LOST
			return
