class_name SubtractiveStatData
extends BaseUpgradeableStatData

@export var reduction_per_level: float = 0.01
@export var min_value: float = 0.05

func calculate_value(base_value: Variant, upgrade_level: int) -> Variant:
	if base_value is int:
		var calculated: int = base_value - int(upgrade_level * reduction_per_level)
		return max(int(min_value), calculated)
	elif base_value is float:
		var calculated: float = base_value - (upgrade_level * reduction_per_level)
		return max(min_value, calculated)
	else:
		var base_as_float: float = str(base_value).to_float()
		var calculated: float = base_as_float - (upgrade_level * reduction_per_level)
		return max(min_value, calculated)
