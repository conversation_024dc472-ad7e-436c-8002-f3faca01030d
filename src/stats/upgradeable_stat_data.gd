class_name BaseUpgradeableStatData
extends Resource

@export var stat_id: StringName
@export var display_name: String
@export_multiline var description: String

@export_group("Cost")
@export var base_cost: int = 10
@export var cost_increase_per_level: int = 5

func calculate_value(base_value: Variant, _upgrade_level: int) -> Variant:
	return base_value

func get_upgrade_cost(current_level: int) -> int:
	return base_cost + (current_level * cost_increase_per_level)
