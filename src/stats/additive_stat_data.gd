class_name AdditiveStatData
extends BaseUpgradeableStatData

@export var value_per_level: float = 1.0

func calculate_value(base_value: Variant, upgrade_level: int) -> Variant:
	if base_value is int:
		return base_value + int(upgrade_level * value_per_level)
	elif base_value is float:
		return base_value + (upgrade_level * value_per_level)
	else:
		return base_value + (upgrade_level * value_per_level)
