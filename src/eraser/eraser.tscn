[gd_scene load_steps=13 format=3 uid="uid://bxy0123eraser"]

[ext_resource type="Script" uid="uid://bcaxr0585brbo" path="res://src/eraser/eraser.gd" id="1_eraser"]
[ext_resource type="Script" uid="uid://c1gtewldstgdg" path="res://src/movement/movement_data.gd" id="3_gq0yk"]
[ext_resource type="PackedScene" uid="uid://k2v1icx8llrc" path="res://src/movement/movement_component.tscn" id="3_movement"]
[ext_resource type="PackedScene" uid="uid://db6e8chc7nyk3" path="res://src/time_dilation/time_sensitive_component.tscn" id="4_time_sensitive"]
[ext_resource type="Script" uid="uid://clrphpo8v8ep1" path="res://src/time_dilation/time_sensitive_data.gd" id="5_time_data"]
[ext_resource type="PackedScene" uid="uid://b2minqce3p1bg" path="res://src/health/health_component.tscn" id="6_health"]
[ext_resource type="Script" uid="uid://chn55a5u5s7ol" path="res://src/health/health_data.gd" id="7_health_data"]
[ext_resource type="Texture2D" uid="uid://bxu67k15ktat" path="res://assets/eraser.png" id="11_gq0yk"]

[sub_resource type="Resource" id="Resource_fnfpw"]
script = ExtResource("3_gq0yk")
tile_size = 8
move_duration = 0.2
metadata/_custom_type_script = "uid://c1gtewldstgdg"

[sub_resource type="Resource" id="Resource_time_data"]
script = ExtResource("5_time_data")
slow_speed = 25.0
normal_speed = 150.0
slow_rotation_speed = 1.0
normal_rotation_speed = 5.0

[sub_resource type="Resource" id="Resource_health_data"]
script = ExtResource("7_health_data")
max_health = 1
current_health = 1

[sub_resource type="CircleShape2D" id="CircleShape2D_eraser"]
radius = 3.0

[node name="Eraser" type="CharacterBody2D"]
collision_mask = 3
motion_mode = 1
script = ExtResource("1_eraser")

[node name="MovementComponent" parent="." node_paths=PackedStringArray("actor", "collision_ray") instance=ExtResource("3_movement")]
data = SubResource("Resource_fnfpw")
actor = NodePath("..")
collision_ray = NodePath("../CollisionRay")

[node name="TimeSensitiveComponent" parent="." instance=ExtResource("4_time_sensitive")]
data = SubResource("Resource_time_data")

[node name="HealthComponent" parent="." instance=ExtResource("6_health")]
data = SubResource("Resource_health_data")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture_filter = 1
texture = ExtResource("11_gq0yk")

[node name="Collision" type="CollisionShape2D" parent="."]
shape = SubResource("CircleShape2D_eraser")

[node name="Hitbox" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="Hitbox"]
shape = SubResource("CircleShape2D_eraser")

[node name="CollisionRay" type="RayCast2D" parent="."]
target_position = Vector2(0, 0)
