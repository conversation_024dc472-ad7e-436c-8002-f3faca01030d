class_name StatUpgradePanel
extends Control

signal upgrade_requested(stat_id: StringName)

@export var stat_name_label: Label
@export var stat_description_label: Label
@export var current_level_label: Label
@export var cost_label: Label
@export var upgrade_button: Button

var _stat_data: BaseUpgradeableStatData

func _ready() -> void:
	upgrade_button.pressed.connect(_on_upgrade_button_pressed)

func setup(stat_data: BaseUpgradeableStatData) -> void:
	_stat_data = stat_data
	stat_name_label.text = stat_data.display_name
	stat_description_label.text = stat_data.description

func update_view(current_level: int, cost: int, player_currency: int) -> void:
	current_level_label.text = "Уровень: " + str(current_level)

	cost_label.text = "Стоимость: " + str(cost)

	upgrade_button.disabled = (player_currency < cost)
	if player_currency < cost:
		upgrade_button.text = "Недостаточно средств"
	else:
		upgrade_button.text = "Улучшить"

func _on_upgrade_button_pressed() -> void:
	upgrade_requested.emit(_stat_data.stat_id)
