class_name <PERSON><PERSON>
extends BaseLevel

signal start_run

@export var portal: Portal
@export var meta_currency_label: Label
@export var upgrade_list_container: VBoxContainer
@export var upgrade_panel_scene: PackedScene

var _upgrade_panels: Array[StatUpgradePanel] = []

func _ready() -> void:
	super._ready()
	portal.player_entered.connect(_on_portal_player_entered)
	GameProgress.progress_changed.connect(_update_upgrade_ui)
	_build_upgrade_ui()
	_update_upgrade_ui()

func prepare_scene() -> void:
	tile_query_system.build_map()

func _on_portal_player_entered() -> void:
	start_run.emit()

func _update_meta_currency_display() -> void:
	var currency: int = GameProgress.get_meta_currency()
	meta_currency_label.text = "Мета-валюта: " + str(currency)

func _build_upgrade_ui() -> void:
	if not upgrade_list_container or not upgrade_panel_scene:
		return

	for stat_data in GameProgress.get_upgradeable_stats():
		var panel_instance: StatUpgradePanel = upgrade_panel_scene.instantiate()
		upgrade_list_container.add_child(panel_instance)
		panel_instance.setup(stat_data)
		panel_instance.upgrade_requested.connect(_on_stat_upgrade_requested)
		_upgrade_panels.append(panel_instance)

func _update_upgrade_ui() -> void:
	_update_meta_currency_display()

	var player_currency: int = GameProgress.get_meta_currency()

	for panel in _upgrade_panels:
		if panel._stat_data:
			var current_level: int = GameProgress.get_stat_upgrade_level(panel._stat_data.stat_id)
			var cost: int = GameProgress.get_stat_upgrade_cost(panel._stat_data.stat_id)
			panel.update_view(current_level, cost, player_currency)

func _on_stat_upgrade_requested(stat_id: StringName) -> void:
	if GameProgress.upgrade_stat(stat_id):
		print("Улучшен стат: ", stat_id)
