[gd_scene load_steps=2 format=3 uid="uid://dhf7god1f8kxn"]

[ext_resource type="Script" uid="uid://cqynm5a5ixadw" path="res://src/progress_bar/event_marker.gd" id="2_event_marker"]

[node name="EventMarker" type="Node2D" node_paths=PackedStringArray("icon_sprite")]
script = ExtResource("2_event_marker")
icon_sprite = NodePath("Icon")

[node name="Icon" type="Sprite2D" parent="."]
texture_filter = 1
position = Vector2(0, -9)

[node name="Line" type="ColorRect" parent="."]
offset_top = -4.0
offset_right = 1.0
offset_bottom = -1.0
color = Color(0.0980392, 0.129412, 0.254902, 1)
