[gd_scene load_steps=5 format=3 uid="uid://bv3kc8uwrke6f"]

[ext_resource type="Script" uid="uid://6bqpdastrjla" path="res://src/progress_bar/progress_bar.gd" id="1_jl4r8"]
[ext_resource type="Texture2D" uid="uid://croipc1prk67w" path="res://assets/paint_bar_texture.png" id="1_x8s5q"]
[ext_resource type="PackedScene" uid="uid://dhf7god1f8kxn" path="res://src/progress_bar/event_marker.tscn" id="2_jl4r8"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_x8s5q"]
bg_color = Color(1, 0.913725, 0.133333, 1)

[node name="ProgressBar" type="Node2D" node_paths=PackedStringArray("progress_bar", "margin_container", "markers_container")]
script = ExtResource("1_jl4r8")
progress_bar = NodePath("NinePatchRect/MarginContainer/ProgressBar")
margin_container = NodePath("NinePatchRect/MarginContainer")
event_marker_scene = ExtResource("2_jl4r8")
markers_container = NodePath("MarkersContainer")

[node name="NinePatchRect" type="NinePatchRect" parent="."]
texture_filter = 1
offset_right = 120.0
offset_bottom = 8.0
texture = ExtResource("1_x8s5q")
region_rect = Rect2(0, 0, 8, 8)
patch_margin_left = 1
patch_margin_top = 1
patch_margin_right = 1
patch_margin_bottom = 1

[node name="MarginContainer" type="MarginContainer" parent="NinePatchRect"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_constants/margin_left = 2
theme_override_constants/margin_top = 2
theme_override_constants/margin_right = 2
theme_override_constants/margin_bottom = 2

[node name="ProgressBar" type="ProgressBar" parent="NinePatchRect/MarginContainer"]
layout_mode = 2
theme_override_styles/fill = SubResource("StyleBoxFlat_x8s5q")
max_value = 1.0
step = 0.001
value = 0.53
show_percentage = false

[node name="MarkersContainer" type="Node2D" parent="."]
