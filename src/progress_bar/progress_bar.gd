extends Node2D

@export var progress_bar: ProgressBar
@export var margin_container: <PERSON>ginContainer
@export var level_state_component: LevelStateComponent
@export var event_marker_scene: PackedScene
@export var markers_container: Node2D

var markers: Array[Node2D] = []

func _ready() -> void:
	level_state_component.data.progress_changed.connect(_on_progress_changed)
	level_state_component.data.event_triggered.connect(_on_event_triggered)
	_on_progress_changed(level_state_component.data.get_progress())
	_populate_event_markers.call_deferred()

func _on_progress_changed(new_progress: float) -> void:
	progress_bar.value = new_progress

func _on_event_triggered(event_index: int) -> void:
	if event_index < 0 or event_index >= markers.size():
		return

	var marker_to_remove: Node2D = markers[event_index]
	if is_instance_valid(marker_to_remove):
		markers[event_index] = null
		marker_to_remove.queue_free()

func _populate_event_markers() -> void:
	for child in markers_container.get_children():
		child.queue_free()
	markers.clear()

	var bar_width: float = progress_bar.size.x
	var data: LevelStateData = level_state_component.data

	for i in range(data.events.size()):
		var event_data: LevelEventData = data.events[i]
		var marker: EventMarker = event_marker_scene.instantiate() as EventMarker
		marker.name = "EventMarker_" + str(i)

		print(event_data.icon)

		marker.set_icon(event_data.icon)

		markers_container.add_child(marker)
		markers.append(marker)

		var left_margin: float = margin_container.get_theme_constant("margin_left")
		var right_margin: float = margin_container.get_theme_constant("margin_right")
		var usable_width: float = bar_width - left_margin - right_margin

		var position_x: float = left_margin + (event_data.progress_threshold * usable_width)
		marker.position = Vector2(position_x, 0)
