extends Node

const SAVE_PATH = "user://progress.dat"

signal progress_changed

var _upgradeable_stats: Array[BaseUpgradeableStatData] = []
var _completed_levels: Dictionary = {}
var _meta_currency: int = 0
var _stat_upgrades: Dictionary = {}

func _ready() -> void:
	_initialize_upgradeable_stats()
	load_progress()

	if _meta_currency == 0:
		_meta_currency = 100

func _initialize_upgradeable_stats() -> void:
	if _upgradeable_stats.is_empty():
		var max_health_upgrade: BaseUpgradeableStatData = load("res://resources/upgrades/max_health_upgrade.tres")
		var max_paint_upgrade: BaseUpgradeableStatData = load("res://resources/upgrades/max_paint_upgrade.tres")
		var move_duration_upgrade: BaseUpgradeableStatData = load("res://resources/upgrades/move_duration_upgrade.tres")
		var extra_lives_upgrade: BaseUpgradeableStatData = load("res://resources/upgrades/extra_lives_upgrade.tres")
		var size_modifier_upgrade: BaseUpgradeableStatData = load("res://resources/upgrades/size_modifier_upgrade.tres")

		_upgradeable_stats = [
			max_health_upgrade,
			max_paint_upgrade,
			move_duration_upgrade,
			extra_lives_upgrade,
			size_modifier_upgrade
		]

		for stat_data in _upgradeable_stats:
			if not _stat_upgrades.has(stat_data.stat_id):
				_stat_upgrades[stat_data.stat_id] = 0

func is_level_completed(level_id: String) -> bool:
	return _completed_levels.has(level_id)

func mark_level_as_completed(level_id: String) -> void:
	if not level_id.is_empty():
		_completed_levels[level_id] = true
		save_progress()

func add_meta_currency(amount: int) -> void:
	if amount > 0:
		_meta_currency += amount
		save_progress()
		progress_changed.emit()

func get_meta_currency() -> int:
	return _meta_currency

func get_upgradeable_stats() -> Array[BaseUpgradeableStatData]:
	return _upgradeable_stats

func get_stat_data(stat_id: StringName) -> BaseUpgradeableStatData:
	for stat_data in _upgradeable_stats:
		if stat_data.stat_id == stat_id:
			return stat_data
	return null

func get_stat_upgrade_cost(stat_id: StringName) -> int:
	var stat_data: BaseUpgradeableStatData = get_stat_data(stat_id)
	if stat_data == null:
		return -1

	var current_level: int = get_stat_upgrade_level(stat_id)
	return stat_data.base_cost + (current_level * stat_data.cost_increase_per_level)

func upgrade_stat(stat_id: StringName) -> bool:
	var cost: int = get_stat_upgrade_cost(stat_id)
	if cost < 0 or _meta_currency < cost:
		return false

	if not _stat_upgrades.has(stat_id):
		_stat_upgrades[stat_id] = 0

	_meta_currency -= cost
	_stat_upgrades[stat_id] += 1
	save_progress()
	progress_changed.emit()
	return true

func get_stat_upgrade_level(stat_name: String) -> int:
	return _stat_upgrades.get(stat_name, 0)

func save_progress() -> void:
	var file: FileAccess = FileAccess.open(SAVE_PATH, FileAccess.WRITE)
	if file:
		var save_data: Dictionary = {
			"completed_levels": _completed_levels,
			"meta_currency": _meta_currency,
			"stat_upgrades": _stat_upgrades
		}
		var json_string: String = JSON.stringify(save_data)
		file.store_string(json_string)

func load_progress() -> void:
	if not FileAccess.file_exists(SAVE_PATH):
		return

	var file: FileAccess = FileAccess.open(SAVE_PATH, FileAccess.READ)
	if file:
		var json_string: String = file.get_as_text()
		var parse_result: Variant = JSON.parse_string(json_string)
		if parse_result is Dictionary:
			var save_data: Dictionary = parse_result
			if save_data.has("completed_levels") and save_data["completed_levels"] is Dictionary:
				_completed_levels = save_data["completed_levels"]
			elif parse_result is Dictionary:
				_completed_levels = parse_result

			if save_data.has("meta_currency") and save_data["meta_currency"] is int:
				_meta_currency = save_data["meta_currency"]

			if save_data.has("stat_upgrades") and save_data["stat_upgrades"] is Dictionary:
				_stat_upgrades = save_data["stat_upgrades"]
