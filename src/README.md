**Название игры:** (Рабочее название: Fruit Rogue)

**Жанр:** Аркадный рогалик с механиками контроля территории и менеджментом ресурсов.

**Ключевые геймплейные механики:**

1.  **Система Ресурсов:**
    *   **Краска (Топливо):** Основной расходуемый ресурс, находящийся в "баке" игрока. Тратится по 1 единице за каждый закрашенный тайл. Также может расходоваться на способности от некоторых артефактов. Восполняется подбором капель краски на уровне.
    *   **Закрашенные Тайлы (Валюта):** Накопительный счетчик всех уникальных тайлов, закрашенных игроком за текущий забег. Это единственная валюта, которая тратится в магазинах на покупку артефактов.

2.  **Покраска и Цель:**
    *   При перемещении на незакрашенную клетку, игрок **тратит 1 единицу Краски (Топлива)**, чтобы ее закрасить. После этого счетчик валюты "Закрашенные Тайлы" увеличивается на 1. Если у игрока нет Краски (Топлива), он не может закрашивать новые тайлы.
    *   Цель в каждой "боевой" комнате — закрасить 100% доступных для покраски тайлов, чтобы открыть выход.

3.  **Взаимодействие с врагами (Пилами):**
    *   Базовый геймплей построен на уклонении от движущихся пил. Игрок начинает забег без каких-либо атакующих способностей.
    *   Существуют разные типы пил с разным поведением.

4.  **Свойства комнат:**
    *   **Комнаты-бублики:** Особый тип комнат, где все сущности (игрок и враги) могут перемещаться за границу экрана и появляться с противоположной стороны.

**Система артефактов:**

Игрок начинает каждый забег без артефактов. Все способности, баффы и пассивные эффекты приобретаются во время забега в виде артефактов.

**1. Артефакты, изменяющие правила боя и движения (предложенные в обсуждении):**
*   **"Прыжок-убийство":** Позволяет перемещаться через одну клетку, уничтожая стандартную пилу на ней.
*   **"Разрушающий удар":** Позволяет разрушать специальные блоки. Во время разрушения игрок уязвим, так как мир вокруг него временно ускоряется.
*   **"Призрачный шаг":** Позволяет игроку проходить сквозь границы экрана в любой комнате.

**2. Артефакты, изменяющие правила покраски и экономики (предложенные в обсуждении):**
*   **"Замыкание контура":** Дает бонусную валюту за закрашивание областей путем рисования замкнутой линии.
*   **"Соединение островов":** Дает бонусную валюту за соединение двух закрашенных участков в один.

**3. Артефакты, основанные на существующих механиках (из `AbilityData`):**
*   **"Фазовый сдвиг" (WallPhaseAbilityData):** Позволяет игроку проходить сквозь стены. При попытке движения в стену тратится Краска (Топливо), и игрок телепортируется до первой свободной клетки в этом направлении.
*   **"Переработка чернил" (RestorePaintAbilityData):** Восстанавливает 1 единицу Краски (Топлива) каждый раз, когда игрок наступает на уже закрашенную клетку.
*   **"Рог изобилия" (MultiDropAbilityData):** Увеличивает количество капель краски, одновременно присутствующих на уровне.
*   **"Стабилизатор времени" (MaintainNormalTimeAbilityData):** Предотвращает ускорение времени, когда игрок движется по уже закрашенным клеткам. Враги остаются замедленными, позволяя безопасно маневрировать по своей территории.

**4. Система Проклятых Артефактов:**
*   Некоторые особо мощные артефакты при покупке дополнительно добавляют в инвентарь игрока отдельный, **Проклятый Артефакт** с негативным эффектом.
*   *Пример Проклятого Артефакта 1:* **"Недолговечные Чернила"**. Закрашенные тайлы начинают со временем исчезать (первым исчезает самый старый).
*   *Пример Проклятого Артефакта 2:* **"Инверсия" (InvertedControlsAbilityData)**. Инвертирует управление игрока. Может быть временным (как в коде) или постоянным проклятием на забег.

**Основной геймплейный цикл (Core Gameplay Loop):**

1.  **Вход в комнату:** Игрок появляется в комнате с врагами, каплями краски и незакрашенными тайлами.
2.  **Фаза менеджмента и покраски:** Игрок маневрирует между врагами, тратя **Краску (Топливо)** для покраски тайлов, что генерирует ему **Валюту**. Он должен следить за запасом Топлива и вовремя подбирать капли краски.
3.  **Зачистка комнаты:** Игрок закрашивает 100% тайлов, и выход открывается.
4.  **Переход:** Игрок переходит в следующую комнату (боевая, магазин, босс).
5.  **Магазин:** В комнатах-магазинах игрок тратит накопленную **Валюту ("Закрашенные Тайлы")** на покупку артефактов.
6.  **Повторение:** Цикл повторяется до встречи с боссом.

**Система прогрессии игрока:**

*   **Прогрессия внутри забега:** Построена исключительно на сборе и комбинировании артефактов. Игрок формирует уникальный билд способностей для каждого забега.
*   **Мета-прогрессия (между забегами):**
    *   После победы над боссом появляется "банкомат". Игрок может конвертировать **все свои оставшиеся, не потраченные в магазинах "Закрашенные Тайлы"** в мета-валюту.
    *   В хабе (вне забега) мета-валюта тратится на постоянное улучшение базовых статов персонажа: `scale`, `sugar`, `juice`, `fiber`, `shell`.

**Уникальные особенности геймплея (USP):**

*   **Экономика Конвертации "Топливо в Валюту":** Игрок активно тратит один конечный ресурс (Краску-топливо) для генерации другого (Закрашенные тайлы-валюту), что создает постоянный цикл риска, менеджмента ресурсов и поиска восполнения.
*   **Чистый билд-ориентированный геймплей:** Прогрессия в забеге зависит исключительно от выбора артефактов и их синергии, а не от числовых улучшений.
*   **Напряжение экономики "Сейчас или Потом":** Потратив валюту на артефакты для усиления в текущем забеге, игрок уменьшает количество валюты, которую сможет конвертировать в постоянную мета-прогрессию в конце.
*   **Модульная система риска:** Проклятия реализованы как отдельные артефакты, что позволяет гибко балансировать игру, привязывая стандартизированные негативные эффекты к разным мощным бонусам.
