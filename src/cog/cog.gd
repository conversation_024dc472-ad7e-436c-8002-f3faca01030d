extends CharacterBody2D

@export var sprite: Sprite2D
@export var time_sensitive_component: TimeSensitiveComponent

var direction: Vector2 = Vector2.ZERO

func _ready() -> void:
	direction = Vector2.RIGHT.rotated(randf_range(0, TAU))

func _physics_process(delta: float) -> void:
	sprite.rotate(time_sensitive_component.current_rotation_speed * delta)

	velocity = direction * time_sensitive_component.current_speed
	var collision: KinematicCollision2D = move_and_collide(velocity * delta)
	if collision:
		direction = direction.bounce(collision.get_normal())
