[gd_scene load_steps=2 format=3 uid="uid://cb5k3begpk4sc"]

[ext_resource type="Script" uid="uid://c3bxkbvpniqlr" path="res://src/win_ui/win_ui.gd" id="1_0"]

[node name="WinUI" type="CanvasLayer" node_paths=PackedStringArray("play_again_button")]
script = ExtResource("1_0")
play_again_button = NodePath("Control/VBoxContainer/PlayAgainButton")

[node name="Control" type="Control" parent="."]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="ColorRect" type="ColorRect" parent="Control"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0, 0, 0, 0.5)

[node name="VBoxContainer" type="VBoxContainer" parent="Control"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -100.0
offset_top = -50.0
offset_right = 100.0
offset_bottom = 50.0
grow_horizontal = 2
grow_vertical = 2

[node name="WinLabel" type="Label" parent="Control/VBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 100
text = "Победа!"
horizontal_alignment = 1

[node name="PlayAgainButton" type="Button" parent="Control/VBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 70
text = "Играть снова"
