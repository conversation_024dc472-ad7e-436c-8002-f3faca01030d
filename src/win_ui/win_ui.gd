extends CanvasLayer

signal play_again_pressed

@export var play_again_button: Button

var level_node: Node2D

func _ready() -> void:
	play_again_button.pressed.connect(_on_play_again_pressed)
	visibility_changed.connect(_on_visibility_changed)
	level_node = get_parent() as Node2D
	hide()

func _on_visibility_changed() -> void:
	show()

	if not is_instance_valid(level_node):
		return
	if not level_node.has_method("get"):
		return
	var level_id: Variant = level_node.get("level_id")
	if not level_id is String:
		return
	var level_id_str: String = level_id
	if level_id_str.is_empty():
		return

	GameProgress.mark_level_as_completed(level_id_str)

func _on_play_again_pressed() -> void:
	play_again_pressed.emit()
