[gd_resource type="Resource" script_class="AdditiveStatData" load_steps=2 format=3 uid="uid://bqx8h4n5m2k7q"]

[ext_resource type="Script" path="res://src/stats/additive_stat_data.gd" id="1_max_health"]

[resource]
script = ExtResource("1_max_health")
stat_id = &"max_health"
display_name = "Макс. здоровье"
description = "Увеличивает максимальный запас здоровья на 1"
base_cost = 10
cost_increase_per_level = 5
value_per_level = 1.0
